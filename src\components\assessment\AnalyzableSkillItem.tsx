
"use client";

import type { AssessedSkill, PortageSkillItem, SkillStatus, ProgressStatus } from '@/lib/types';
import { SKILL_STATUS_OPTIONS, PROGRESS_STATUS_OPTIONS, MOCK_ASSESSMENTS_DATA } from '@/lib/constants'; // Added MOCK_ASSESSMENTS_DATA
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, DialogClose, DialogFooter } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { DatePicker } from '@/components/ui/date-picker';
import { useState, useRef, useEffect }from 'react';
import { useRouter } from 'next/navigation'; // Added useRouter
import { <PERSON>ader2, <PERSON>rkles, BookOpen, Printer, CalendarClock, CheckCircle, Edit3, AlertTriangle, Save } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { analyzeSkillForDailyRoutine, SkillAnalysisInput, SkillAnalysisOutput } from '@/ai/flows/analyze-skill-for-daily-routine';
import { analyzeSkillForPreschoolRoutine, PreschoolSkillAnalysisInput, PreschoolSkillAnalysisOutput } from '@/ai/flows/analyze-skill-for-preschool-routine';
import { formatDate } from '@/lib/utils';
import { parseISO, isPast, format } from 'date-fns'; // Added format

// Combined type for the item prop
export type AnalyzableItemType = AssessedSkill & 
  (Omit<PortageSkillItem, 'id'> & { 
    skillId: string; 
    dimensionName: string; 
    subCategoryName: string; 
  });


interface AnalyzableSkillItemProps {
  item: AnalyzableItemType;
  childName: string;
  assessmentId: string; // Added assessmentId to help find the assessment to update
}

export default function AnalyzableSkillItem({ item, childName, assessmentId }: AnalyzableSkillItemProps) {
  const router = useRouter();
  const { toast } = useToast();

  // AI Analysis States
  const [isLoadingHome, setIsLoadingHome] = useState(false);
  const [homeAnalysisData, setHomeAnalysisData] = useState<SkillAnalysisOutput | null>(null);
  const [isHomeDialogOpen, setIsHomeDialogOpen] = useState(false);
  const homeAnalysisContentRef = useRef<HTMLDivElement>(null);

  const [isLoadingPreschool, setIsLoadingPreschool] = useState(false);
  const [preschoolAnalysisData, setPreschoolAnalysisData] = useState<PreschoolSkillAnalysisOutput | null>(null);
  const [isPreschoolDialogOpen, setIsPreschoolDialogOpen] = useState(false);
  const preschoolAnalysisContentRef = useRef<HTMLDivElement>(null);

  // Progress Tracking Dialog States
  const [isProgressDialogOpen, setIsProgressDialogOpen] = useState(false);
  const [currentProgressStatus, setCurrentProgressStatus] = useState<ProgressStatus | undefined>(item.progressStatus || 'pending');
  const [currentImplementationStartDate, setCurrentImplementationStartDate] = useState<Date | undefined>(
    item.implementationStartDate ? parseISO(item.implementationStartDate) : undefined
  );
  const [currentTargetCompletionDate, setCurrentTargetCompletionDate] = useState<Date | undefined>(
    item.targetCompletionDate ? parseISO(item.targetCompletionDate) : undefined
  );
  const [currentProgressNotes, setCurrentProgressNotes] = useState(item.progressNotes || '');

  // Extend Deadline Dialog States
  const [isExtendDeadlineDialogOpen, setIsExtendDeadlineDialogOpen] = useState(false);
  const [newTargetDateForExtension, setNewTargetDateForExtension] = useState<Date | undefined>(
    item.targetCompletionDate ? parseISO(item.targetCompletionDate) : undefined
  );

  const [isTargetDateOverdue, setIsTargetDateOverdue] = useState(false);

  const statusInfo = SKILL_STATUS_OPTIONS.find(s => s.value === item.status);
  const progressStatusInfo = PROGRESS_STATUS_OPTIONS.find(ps => ps.value === item.progressStatus);

  useEffect(() => {
    if (item.targetCompletionDate) {
      try {
        const targetDate = parseISO(item.targetCompletionDate);
        setIsTargetDateOverdue(isPast(targetDate) && item.progressStatus !== 'mastered');
      } catch (e) {
        setIsTargetDateOverdue(false);
      }
    } else {
      setIsTargetDateOverdue(false);
    }
    // Update dialog fields if item prop changes
    setCurrentProgressStatus(item.progressStatus || 'pending');
    setCurrentImplementationStartDate(item.implementationStartDate ? parseISO(item.implementationStartDate) : undefined);
    setCurrentTargetCompletionDate(item.targetCompletionDate ? parseISO(item.targetCompletionDate) : undefined);
    setCurrentProgressNotes(item.progressNotes || '');
    setNewTargetDateForExtension(item.targetCompletionDate ? parseISO(item.targetCompletionDate) : undefined);

  }, [item.targetCompletionDate, item.progressStatus, item.implementationStartDate, item.progressNotes]);


  const handlePrint = (title: string, contentRef: React.RefObject<HTMLDivElement>) => {
    if (!contentRef.current) {
      toast({ title: "خطأ في الطباعة", description: "لم يتم العثور على المحتوى للطباعة.", variant: "destructive" });
      return;
    }
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write('<html><head><title>' + title + '</title>');
      printWindow.document.write(`
        <style>
          body { font-family: 'Tajawal', 'Noto Sans Arabic', system-ui, sans-serif; direction: rtl; padding: 20px; margin: 0; }
          h1, h2, h3, h4, h5, h6 { color: #333; margin-bottom: 0.5em; margin-top: 1em; }
          p { margin-bottom: 1em; line-height: 1.6; white-space: pre-wrap; font-family: inherit; }
          .dialog-description { color: #555; font-size: 0.9em; margin-bottom: 1.5em; }
          .analysis-section h4 { font-weight: bold; color: hsl(var(--primary)); margin-bottom: 0.5em; }
          .analysis-section p { white-space: pre-wrap; background-color: hsl(var(--muted) / 0.3); padding: 8px; border-radius: 4px; border: 1px solid hsl(var(--border)); color: hsl(var(--foreground) / 0.9); }
          @media print {
            body { -webkit-print-color-adjust: exact; print-color-adjust: exact; }
            @page { margin: 20mm; }
            button, .no-print { display: none !important; }
          }
        </style>
      `);
      printWindow.document.write('</head><body dir="rtl">');
      printWindow.document.write('<h1>' + title + '</h1>');
      const skillDescription = `المهارة: ${item.behavior} (${item.ageRange}) للطفل ${childName}`;
      printWindow.document.write('<p class="dialog-description">' + skillDescription + '</p>');
      
      const sections = contentRef.current.querySelectorAll('div > h4, div > p');
      let currentSectionHtml = '';
      sections.forEach(el => {
          if(el.tagName.toLowerCase() === 'h4') {
              if(currentSectionHtml) {
                  printWindow.document.write('<div class="analysis-section">' + currentSectionHtml + '</div>');
                  currentSectionHtml = '';
              }
          }
          currentSectionHtml += el.outerHTML;
      });
      if(currentSectionHtml) { 
          printWindow.document.write('<div class="analysis-section">' + currentSectionHtml + '</div>');
      }

      printWindow.document.write('</body></html>');
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
    } else {
      toast({ title: "خطأ", description: "يرجى السماح بالنوافذ المنبثقة لطباعة هذا المحتوى.", variant: "destructive"});
    }
  };

  const handleAnalyzeSkillForHome = async () => {
    setIsLoadingHome(true);
    setHomeAnalysisData(null);
    try {
      const input: SkillAnalysisInput = {
        skillBehavior: item.behavior,
        ageRange: item.ageRange,
        childName: childName,
      };
      const result = await analyzeSkillForDailyRoutine(input);
      setHomeAnalysisData(result);
      setIsHomeDialogOpen(true);
    } catch (error) {
      console.error("Error analyzing skill for home:", error);
      toast({
        title: "خطأ في تحليل خطة الأسرة",
        description: "لم نتمكن من تحليل المهارة للروتين المنزلي في الوقت الحالي. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
      setIsHomeDialogOpen(false);
    } finally {
      setIsLoadingHome(false);
    }
  };

  const handleAnalyzeSkillForPreschool = async () => {
    setIsLoadingPreschool(true);
    setPreschoolAnalysisData(null);
    try {
      const input: PreschoolSkillAnalysisInput = {
        skillBehavior: item.behavior,
        ageRange: item.ageRange,
        childName: childName,
      };
      const result = await analyzeSkillForPreschoolRoutine(input);
      setPreschoolAnalysisData(result);
      setIsPreschoolDialogOpen(true);
    } catch (error) {
      console.error("Error analyzing skill for preschool:", error);
      toast({
        title: "خطأ في تحليل خطة الروضة",
        description: "لم نتمكن من تحليل المهارة لروتين الروضة/الحضانة في الوقت الحالي. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
      setIsPreschoolDialogOpen(false);
    } finally {
      setIsLoadingPreschool(false);
    }
  };

  const handleSaveProgress = () => {
    const assessmentIndex = MOCK_ASSESSMENTS_DATA.findIndex(a => a.id === assessmentId);
    if (assessmentIndex === -1) {
      toast({ title: "خطأ", description: "لم يتم العثور على التقييم.", variant: "destructive" });
      return;
    }
    const skillIndex = MOCK_ASSESSMENTS_DATA[assessmentIndex].assessedSkills.findIndex(s => s.skillId === item.skillId);
    if (skillIndex === -1) {
      toast({ title: "خطأ", description: "لم يتم العثور على المهارة ضمن التقييم.", variant: "destructive" });
      return;
    }

    MOCK_ASSESSMENTS_DATA[assessmentIndex].assessedSkills[skillIndex] = {
      ...MOCK_ASSESSMENTS_DATA[assessmentIndex].assessedSkills[skillIndex],
      progressStatus: currentProgressStatus,
      implementationStartDate: currentImplementationStartDate ? format(currentImplementationStartDate, 'yyyy-MM-dd') : undefined,
      targetCompletionDate: currentTargetCompletionDate ? format(currentTargetCompletionDate, 'yyyy-MM-dd') : undefined,
      progressNotes: currentProgressNotes,
    };

    toast({ title: "نجاح", description: "تم تحديث تتبع التقدم للمهارة." });
    setIsProgressDialogOpen(false);
    router.refresh();
  };

  const handleSaveExtendedDeadline = () => {
     if (!newTargetDateForExtension) {
      toast({ title: "خطأ", description: "يرجى تحديد تاريخ إنجاز مستهدف جديد.", variant: "destructive" });
      return;
    }
    const assessmentIndex = MOCK_ASSESSMENTS_DATA.findIndex(a => a.id === assessmentId);
    if (assessmentIndex === -1) {
      toast({ title: "خطأ", description: "لم يتم العثور على التقييم.", variant: "destructive" });
      return;
    }
    const skillIndex = MOCK_ASSESSMENTS_DATA[assessmentIndex].assessedSkills.findIndex(s => s.skillId === item.skillId);
    if (skillIndex === -1) {
      toast({ title: "خطأ", description: "لم يتم العثور على المهارة ضمن التقييم.", variant: "destructive" });
      return;
    }

    MOCK_ASSESSMENTS_DATA[assessmentIndex].assessedSkills[skillIndex].targetCompletionDate = format(newTargetDateForExtension, 'yyyy-MM-dd');
    
    toast({ title: "نجاح", description: "تم تمديد الموعد المستهدف للمهارة." });
    setIsExtendDeadlineDialogOpen(false);
    router.refresh();
  };


  const isTrackableSkill = item.status === 'no' || item.status === 'unclear';

  return (
    <div className="p-3 border rounded-md bg-background hover:bg-muted/50 space-y-2">
      <div className="flex justify-between items-start">
        <div>
          <p className="font-medium">{item.itemNumber}. {item.behavior} <span className="text-xs text-muted-foreground">({item.subCategoryName} - {item.ageRange})</span></p>
          <p className="text-xs text-muted-foreground">الطريقة: {item.applicationMethod}</p>
          {item.tools && <p className="text-xs text-muted-foreground">الأدوات: {item.tools}</p>}
        </div>
        <div className="flex flex-col items-end gap-1">
          {statusInfo && (
            <Badge variant={item.status === 'yes' ? 'default' : item.status === 'no' ? 'destructive' : 'secondary'}
              className={`whitespace-nowrap ${item.status === 'yes' ? 'bg-green-500 text-white' : item.status === 'no' ? 'bg-red-500 text-white' : 'bg-yellow-500 text-black'}`}>
              {statusInfo.symbol} {statusInfo.label}
            </Badge>
          )}
          {progressStatusInfo && (
             <Badge className={`whitespace-nowrap ${progressStatusInfo.colorClass}`}>
                {progressStatusInfo.symbol} {progressStatusInfo.label}
             </Badge>
          )}
        </div>
      </div>
      
      {item.notes && <p className="text-sm text-foreground/80 border-r-2 border-primary pr-2"><strong>ملاحظات التقييم:</strong> {item.notes}</p>}
      
      {(item.progressStatus && item.progressStatus !== 'pending') && (
        <div className="mt-2 p-2 border-t border-dashed space-y-1 text-xs text-muted-foreground">
          <p className="font-semibold text-primary">متابعة الهدف:</p>
          {item.implementationStartDate && <p><strong>تاريخ بدء التنفيذ:</strong> {formatDate(item.implementationStartDate)}</p>}
          {item.targetCompletionDate && (
            <p className={isTargetDateOverdue ? 'text-red-600 font-bold' : ''}>
              <strong>تاريخ الإنجاز المستهدف:</strong> {formatDate(item.targetCompletionDate)}
              {isTargetDateOverdue && <span className="mr-1">(تجاوز الموعد <AlertTriangle className="inline h-3 w-3"/>)</span>}
            </p>
          )}
          {item.progressNotes && <p className="text-sm text-foreground/80 border-r-2 border-accent pr-2"><strong>ملاحظات التقدم:</strong> {item.progressNotes}</p>}
        </div>
      )}

      <div className="mt-2 flex flex-wrap gap-2 items-center">
        <Dialog open={isHomeDialogOpen} onOpenChange={(open) => {
          setIsHomeDialogOpen(open);
          if (!open) setHomeAnalysisData(null); 
        }}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" onClick={handleAnalyzeSkillForHome} disabled={isLoadingHome}>
              {isLoadingHome && !homeAnalysisData ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <Sparkles className="ml-2 h-4 w-4 text-yellow-500" />}
              خطة اسرية
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col">
            <DialogHeader>
              <DialogTitle>تحليل دمج المهارة في الروتين اليومي (الأسرة)</DialogTitle>
              <DialogDescription>المهارة: {item.behavior} ({item.ageRange}) للطفل {childName}</DialogDescription>
            </DialogHeader>
            {isLoadingHome && !homeAnalysisData && <div className="flex-grow flex justify-center items-center p-8"><Loader2 className="h-12 w-12 animate-spin text-primary" /></div>}
            {homeAnalysisData && (
              <div ref={homeAnalysisContentRef} className="space-y-3 py-4 text-sm overflow-y-auto pr-2">
                {Object.entries(homeAnalysisData).map(([key, value]) => {
                    const label = { mealtime: "وقت الطعام:", bathroom: "وقت استخدام الحمام:", playtime: "وقت اللعب:", outings: "عند الخروج من المنزل:", bedtime: "وقت النوم:", toolsIntegration: "استخدام الأدوات:", generalTips: "نصائح عامة للتطبيق:", }[key] || key;
                    return (<div key={key}><h4 className="font-semibold text-primary mb-1">{label}</h4><p className="whitespace-pre-wrap bg-muted/30 p-2 rounded-md border text-foreground/90">{value}</p></div>);
                })}
              </div>
            )}
            <DialogFooter className="gap-2 sm:justify-end">
              <Button variant="outline" onClick={() => handlePrint(`خطة اسرية للمهارة: ${item.behavior}`, homeAnalysisContentRef)} disabled={!homeAnalysisData || isLoadingHome}>
                <Printer className="ml-2 h-4 w-4" /> طباعة الخطة
              </Button>
              <DialogClose asChild><Button variant="outline">إغلاق</Button></DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Dialog open={isPreschoolDialogOpen} onOpenChange={(open) => {
          setIsPreschoolDialogOpen(open);
          if (!open) setPreschoolAnalysisData(null);
        }}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" onClick={handleAnalyzeSkillForPreschool} disabled={isLoadingPreschool}>
              {isLoadingPreschool && !preschoolAnalysisData ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <BookOpen className="ml-2 h-4 w-4 text-green-500" />}
              خطة الروضة / الحضانة
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col">
            <DialogHeader>
              <DialogTitle>تحليل دمج المهارة في روتين الروضة/الحضانة</DialogTitle>
              <DialogDescription>المهارة: {item.behavior} ({item.ageRange}) للطفل {childName}</DialogDescription>
            </DialogHeader>
            {isLoadingPreschool && !preschoolAnalysisData && <div className="flex-grow flex justify-center items-center p-8"><Loader2 className="h-12 w-12 animate-spin text-primary" /></div>}
            {preschoolAnalysisData && (
              <div ref={preschoolAnalysisContentRef} className="space-y-3 py-4 text-sm overflow-y-auto pr-2">
                 {Object.entries(preschoolAnalysisData).map(([key, value]) => {
                    const label = { arrivalTime: "وقت الوصول:", morningCircle: "الدائرة الصباحية:", activityTransition: "الانتقال بين الأنشطة:", learningCenters: "المراكز/أركان التعلم:", outdoorPlay: "الساحة الخارجية:", preschoolBathroom: "وقت الحمام (في الروضة):", preschoolSnackTime: "الوجبة الخفيفة/الغداء (في الروضة):", storyTime: "وقت القصة:", departureTime: "المغادرة:", preschoolToolsIntegration: "استخدام الأدوات (في الروضة):", teacherGeneralTips: "نصائح عامة للتطبيق للمعلمين:",}[key] || key;
                    return (<div key={key}><h4 className="font-semibold text-primary mb-1">{label}</h4><p className="whitespace-pre-wrap bg-muted/30 p-2 rounded-md border text-foreground/90">{value}</p></div>);
                })}
              </div>
            )}
            <DialogFooter className="gap-2 sm:justify-end">
               <Button variant="outline" onClick={() => handlePrint(`خطة الروضة/الحضانة للمهارة: ${item.behavior}`, preschoolAnalysisContentRef)} disabled={!preschoolAnalysisData || isLoadingPreschool}>
                <Printer className="ml-2 h-4 w-4" /> طباعة الخطة
              </Button>
              <DialogClose asChild><Button variant="outline">إغلاق</Button></DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {isTrackableSkill && (
          <>
            <Dialog open={isProgressDialogOpen} onOpenChange={setIsProgressDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                    <Edit3 className="ml-2 h-4 w-4" /> بدء/تحديث التتبع
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>تتبع تقدم المهارة: {item.behavior}</DialogTitle>
                  <DialogDescription>
                    حدد حالة التقدم، تواريخ البدء والإنجاز، وأضف ملاحظات.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div>
                    <Label htmlFor="progressStatus">حالة التقدم</Label>
                    <Select value={currentProgressStatus} onValueChange={(val) => setCurrentProgressStatus(val as ProgressStatus)}>
                      <SelectTrigger id="progressStatus"><SelectValue /></SelectTrigger>
                      <SelectContent>
                        {PROGRESS_STATUS_OPTIONS.map(opt => (
                          <SelectItem key={opt.value} value={opt.value}>{opt.symbol} {opt.label}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="implementationStartDate">تاريخ بدء التنفيذ</Label>
                    <DatePicker date={currentImplementationStartDate} setDate={setCurrentImplementationStartDate} buttonClassName="w-full" />
                  </div>
                  <div>
                    <Label htmlFor="targetCompletionDate">تاريخ الإنجاز المستهدف</Label>
                    <DatePicker date={currentTargetCompletionDate} setDate={setCurrentTargetCompletionDate} buttonClassName="w-full"/>
                  </div>
                  <div>
                    <Label htmlFor="progressNotes">ملاحظات التقدم</Label>
                    <Textarea 
                      id="progressNotes" 
                      value={currentProgressNotes} 
                      onChange={(e) => setCurrentProgressNotes(e.target.value)}
                      placeholder="أدخل ملاحظات حول التقدم المحرز هنا..."
                    />
                  </div>
                </div>
                <DialogFooter>
                  <DialogClose asChild><Button variant="outline">إلغاء</Button></DialogClose>
                  <Button onClick={handleSaveProgress}><Save className="ml-2 h-4 w-4" /> حفظ التقدم</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {item.progressStatus === 'implemented' && item.targetCompletionDate && (
                 <Dialog open={isExtendDeadlineDialogOpen} onOpenChange={setIsExtendDeadlineDialogOpen}>
                    <DialogTrigger asChild>
                        <Button variant="outline" size="sm">
                            <CalendarClock className="ml-2 h-4 w-4" /> تمديد الموعد
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-xs">
                        <DialogHeader>
                        <DialogTitle>تمديد الموعد المستهدف</DialogTitle>
                        <DialogDescription>
                            المهارة: {item.behavior}<br/>
                            الموعد الحالي: {formatDate(item.targetCompletionDate)}
                        </DialogDescription>
                        </DialogHeader>
                        <div className="py-4">
                        <Label htmlFor="newTargetDate">تاريخ الإنجاز المستهدف الجديد</Label>
                        <DatePicker date={newTargetDateForExtension} setDate={setNewTargetDateForExtension} buttonClassName="w-full"/>
                        </div>
                        <DialogFooter>
                        <DialogClose asChild><Button variant="outline">إلغاء</Button></DialogClose>
                        <Button onClick={handleSaveExtendedDeadline}><Save className="ml-2 h-4 w-4" /> حفظ الموعد الجديد</Button>
                        </DialogFooter>
                    </DialogContent>
                 </Dialog>
            )}
          </>
        )}
      </div>
    </div>
  );
}

