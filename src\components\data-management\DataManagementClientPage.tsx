
"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { UploadCloud, DownloadCloud, AlertTriangle, RefreshCw } from 'lucide-react';
import { useDataManagement } from '@/hooks/use-storage';
import type { Child, Assessment, User, LearningPlan, ComprehensiveReport } from '@/lib/types';

interface AppData {
  version: string;
  exportedAt: string;
  data: {
    children: Child[];
    assessments: Assessment[];
    users: User[];
    learningPlans: LearningPlan[];
    comprehensiveReports: ComprehensiveReport[];
  };
}

export default function DataManagementClientPage() {
  const { toast } = useToast();
  const { exportData, importData, storageInfo, refreshStorageInfo } = useDataManagement();
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);

  const handleExportData = () => {
    setIsExporting(true);
    try {
      // Get actual data from localStorage using the storage functions
      const appData = exportData();

      if (!appData) {
        throw new Error("فشل في الحصول على البيانات للتصدير.");
      }

      const jsonData = JSON.stringify(appData, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `portage-plus-data-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "نجاح التصدير",
        description: "تم تصدير بيانات التطبيق بنجاح.",
      });
    } catch (error) {
      console.error("Error exporting data:", error);
      toast({
        title: "خطأ في التصدير",
        description: "حدث خطأ أثناء محاولة تصدير البيانات.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleImportData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      return;
    }

    setIsImporting(true);
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const text = e.target?.result;
        if (typeof text !== 'string') {
          throw new Error("فشل في قراءة الملف.");
        }
        const importedAppData = JSON.parse(text) as AppData;

        // Enhanced validation
        if (
          !importedAppData ||
          !importedAppData.data ||
          !Array.isArray(importedAppData.data.children) ||
          !Array.isArray(importedAppData.data.assessments) ||
          !Array.isArray(importedAppData.data.users)
        ) {
          throw new Error("ملف البيانات غير صالح أو تالف.");
        }

        // Use the proper import function from storage
        const success = importData(importedAppData);

        if (success) {
          // Refresh storage info to reflect the changes
          refreshStorageInfo();

          toast({
            title: "نجاح الاستيراد",
            description: "تم استيراد البيانات بنجاح. سيتم تحديث التطبيق تلقائياً.",
            duration: 5000,
          });

          // Trigger a page reload after a short delay to ensure all components refresh
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        } else {
          throw new Error("فشل في حفظ البيانات المستوردة.");
        }

      } catch (error: any) {
        console.error("Error importing data:", error);
        toast({
          title: "خطأ في الاستيراد",
          description: error.message || "حدث خطأ أثناء محاولة استيراد البيانات.",
          variant: "destructive",
        });
      } finally {
        setIsImporting(false);
        // Reset file input value to allow re-importing the same file if needed
        event.target.value = "";
      }
    };
    reader.readAsText(file);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DownloadCloud className="h-6 w-6 text-primary" />
            تصدير البيانات
          </CardTitle>
          <CardDescription>
            قم بتصدير جميع بيانات التطبيق (الأطفال، التقييمات، المستخدمين، الخطط التعليمية، التقارير الشاملة) كملف JSON للنسخ الاحتياطي أو النقل.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={handleExportData} disabled={isExporting} className="w-full sm:w-auto">
            {isExporting ? "جارٍ التصدير..." : "تصدير البيانات الآن"}
          </Button>

          {storageInfo && (
            <div className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
              <p className="font-medium mb-2">إحصائيات البيانات الحالية:</p>
              <div className="grid grid-cols-2 gap-2">
                <span>الأطفال: {storageInfo.childrenCount}</span>
                <span>التقييمات: {storageInfo.assessmentsCount}</span>
                <span>المستخدمين: {storageInfo.usersCount}</span>
                <span>الخطط التعليمية: {storageInfo.learningPlansCount}</span>
                <span>التقارير الشاملة: {storageInfo.comprehensiveReportsCount}</span>
                <span className="col-span-2">آخر تحديث: {storageInfo.lastSync ? new Date(storageInfo.lastSync).toLocaleString('ar-SA') : 'غير محدد'}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UploadCloud className="h-6 w-6 text-primary" />
            استيراد البيانات
          </CardTitle>
          <CardDescription>
            قم باستيراد بيانات التطبيق من ملف JSON. سيتم استبدال البيانات الحالية بالبيانات الموجودة في الملف.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="importFile" className="block mb-2 text-sm font-medium text-foreground">
              اختر ملف JSON للاستيراد:
            </Label>
            <Input
              id="importFile"
              type="file"
              accept=".json"
              onChange={handleImportData}
              disabled={isImporting}
              className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary/10 file:text-primary hover:file:bg-primary/20"
            />
          </div>
          {isImporting && <p className="text-sm text-muted-foreground">جارٍ استيراد البيانات...</p>}
          <div className="mt-4 p-3 border border-yellow-500 bg-yellow-50 rounded-md text-yellow-700 flex items-start gap-2">
            <AlertTriangle className="h-5 w-5 mt-0.5 text-yellow-600 shrink-0" />
            <div>
              <p className="font-semibold">ملاحظة هامة عند الاستيراد:</p>
              <ul className="list-disc list-inside text-xs space-y-1 mt-1">
                <li>سيتم استبدال جميع البيانات الحالية (الأطفال، التقييمات، المستخدمين، الخطط التعليمية، التقارير الشاملة) بالبيانات الموجودة في الملف الذي تقوم باستيراده.</li>
                <li>تأكد من أن الملف بتنسيق JSON صحيح ومتوافق مع هيكل بيانات التطبيق.</li>
                <li>سيتم إعادة تحميل الصفحة تلقائياً بعد نجاح عملية الاستيراد لتطبيق التغييرات بشكل كامل.</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

