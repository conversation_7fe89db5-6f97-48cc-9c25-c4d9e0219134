
"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { UploadCloud, DownloadCloud, AlertTriangle } from 'lucide-react';
import { MOCK_CHILDREN_DATA, MOCK_ASSESSMENTS_DATA, MOCK_USERS_DATA } from '@/lib/constants';
import type { Child, Assessment, User } from '@/lib/types';

interface AppData {
  version: string;
  exportedAt: string;
  data: {
    children: Child[];
    assessments: Assessment[];
    users: User[];
  };
}

export default function DataManagementClientPage() {
  const { toast } = useToast();
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);

  const handleExportData = () => {
    setIsExporting(true);
    try {
      const appData: AppData = {
        version: "1.0.0", // Simple versioning
        exportedAt: new Date().toISOString(),
        data: {
          children: MOCK_CHILDREN_DATA,
          assessments: MOCK_ASSESSMENTS_DATA,
          users: MOCK_USERS_DATA,
        }
      };

      const jsonData = JSON.stringify(appData, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `portage-plus-data-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "نجاح التصدير",
        description: "تم تصدير بيانات التطبيق بنجاح.",
      });
    } catch (error) {
      console.error("Error exporting data:", error);
      toast({
        title: "خطأ في التصدير",
        description: "حدث خطأ أثناء محاولة تصدير البيانات.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleImportData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      return;
    }

    setIsImporting(true);
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const text = e.target?.result;
        if (typeof text !== 'string') {
          throw new Error("فشل في قراءة الملف.");
        }
        const importedAppData = JSON.parse(text) as AppData;

        // Basic validation
        if (
          !importedAppData ||
          !importedAppData.data ||
          !Array.isArray(importedAppData.data.children) ||
          !Array.isArray(importedAppData.data.assessments) ||
          !Array.isArray(importedAppData.data.users)
        ) {
          throw new Error("ملف البيانات غير صالح أو تالف.");
        }

        // Clear existing mock data and push imported data
        // This is a direct mutation for prototype purposes.
        MOCK_CHILDREN_DATA.length = 0;
        importedAppData.data.children.forEach(child => MOCK_CHILDREN_DATA.push(child));

        MOCK_ASSESSMENTS_DATA.length = 0;
        importedAppData.data.assessments.forEach(assessment => MOCK_ASSESSMENTS_DATA.push(assessment));

        MOCK_USERS_DATA.length = 0;
        importedAppData.data.users.forEach(user => MOCK_USERS_DATA.push(user));
        
        toast({
          title: "نجاح الاستيراد",
          description: "تم استيراد البيانات بنجاح. قد تحتاج إلى إعادة تحميل الصفحة لتطبيق التغييرات في جميع أنحاء التطبيق.",
          duration: 7000, 
        });

      } catch (error: any) {
        console.error("Error importing data:", error);
        toast({
          title: "خطأ في الاستيراد",
          description: error.message || "حدث خطأ أثناء محاولة استيراد البيانات.",
          variant: "destructive",
        });
      } finally {
        setIsImporting(false);
        // Reset file input value to allow re-importing the same file if needed
        event.target.value = ""; 
      }
    };
    reader.readAsText(file);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DownloadCloud className="h-6 w-6 text-primary" />
            تصدير البيانات
          </CardTitle>
          <CardDescription>
            قم بتصدير جميع بيانات التطبيق (الأطفال، التقييمات، المستخدمين) كملف JSON للنسخ الاحتياطي أو النقل.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={handleExportData} disabled={isExporting} className="w-full sm:w-auto">
            {isExporting ? "جارٍ التصدير..." : "تصدير البيانات الآن"}
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UploadCloud className="h-6 w-6 text-primary" />
            استيراد البيانات
          </CardTitle>
          <CardDescription>
            قم باستيراد بيانات التطبيق من ملف JSON. سيتم استبدال البيانات الحالية بالبيانات الموجودة في الملف.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="importFile" className="block mb-2 text-sm font-medium text-foreground">
              اختر ملف JSON للاستيراد:
            </Label>
            <Input
              id="importFile"
              type="file"
              accept=".json"
              onChange={handleImportData}
              disabled={isImporting}
              className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary/10 file:text-primary hover:file:bg-primary/20"
            />
          </div>
          {isImporting && <p className="text-sm text-muted-foreground">جارٍ استيراد البيانات...</p>}
           <div className="mt-4 p-3 border border-yellow-500 bg-yellow-50 rounded-md text-yellow-700 flex items-start gap-2">
             <AlertTriangle className="h-5 w-5 mt-0.5 text-yellow-600 shrink-0" />
             <div>
                <p className="font-semibold">ملاحظة هامة عند الاستيراد:</p>
                <ul className="list-disc list-inside text-xs space-y-1 mt-1">
                    <li>سيتم استبدال جميع البيانات الحالية (الأطفال، التقييمات، المستخدمين) بالبيانات الموجودة في الملف الذي تقوم باستيراده.</li>
                    <li>تأكد من أن الملف بتنسيق JSON صحيح ومتوافق مع هيكل بيانات التطبيق.</li>
                    <li>بعد نجاح عملية الاستيراد، **قد تحتاج إلى إعادة تحميل الصفحة (Refresh)** لتطبيق التغييرات بشكل كامل في جميع أنحاء التطبيق.</li>
                </ul>
             </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

    