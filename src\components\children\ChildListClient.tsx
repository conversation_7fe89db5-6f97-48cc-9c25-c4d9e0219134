"use client";

import React, { useState, useEffect } from 'react';
import type { Child } from '@/lib/types';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { PlusCircle, Search, Edit, Trash2, Upload, X, Users } from 'lucide-react';
import ChildCard from './ChildCard';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { useChildren } from '@/hooks/use-storage';
import { generateChildIdNumber, generateUniqueId } from '@/lib/utils';
import Image from 'next/image'; // For image preview
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select'; // For gender

interface ChildListClientProps {
  initialChildren: Child[];
}

export default function ChildListClient({ initialChildren }: ChildListClientProps) {
  const { children, loading, addChild, updateChild, removeChild } = useChildren();
  const [searchTerm, setSearchTerm] = useState('');
  const [isFormDialogOpen, setIsFormDialogOpen] = useState(false);
  const [editingChild, setEditingChild] = useState<Child | null>(null);
  const [childToDeleteId, setChildToDeleteId] = useState<string | null>(null);
  const { toast } = useToast();

  const [currentChildIdNumber, setCurrentChildIdNumber] = useState('');
  const [currentName, setCurrentName] = useState('');
  const [currentBirthDate, setCurrentBirthDate] = useState('');
  const [currentEnrollmentDate, setCurrentEnrollmentDate] = useState(''); // Added state for enrollment date
  const [currentSpecialistName, setCurrentSpecialistName] = useState('');
  const [currentAvatarUrl, setCurrentAvatarUrl] = useState<string | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [currentGender, setCurrentGender] = useState<Child['gender']>('unknown');


  useEffect(() => {
    if (editingChild) {
      setCurrentChildIdNumber(editingChild.childIdNumber);
      setCurrentName(editingChild.name);
      setCurrentBirthDate(editingChild.birthDate);
      setCurrentEnrollmentDate(editingChild.enrollmentDate); // Set enrollment date when editing
      setCurrentSpecialistName(editingChild.specialistName);
      setCurrentAvatarUrl(editingChild.avatarUrl || null);
      setAvatarPreview(editingChild.avatarUrl || null);
      setCurrentGender(editingChild.gender || 'unknown');
    } else {
      // Reset for new child
      setCurrentChildIdNumber(generateChildIdNumber()); // Auto-generate for new child
      setCurrentName('');
      setCurrentBirthDate('');
      setCurrentEnrollmentDate(''); // Reset enrollment date
      setCurrentSpecialistName('');
      setCurrentAvatarUrl(null);
      setAvatarPreview(null);
      setCurrentGender('unknown');
    }
  }, [editingChild, isFormDialogOpen]);

  const filteredChildren = children.filter(child =>
    child.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleOpenFormDialog = (child: Child | null = null) => {
    setEditingChild(child);
    setIsFormDialogOpen(true);
  };

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const reader = new FileReader();
      reader.onloadend = () => {
        setCurrentAvatarUrl(reader.result as string);
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      if (editingChild && editingChild.avatarUrl) {
        // don't clear if just cancelling file selection
      } else {
        setCurrentAvatarUrl(null);
        setAvatarPreview(null);
      }
    }
  };

  const handleRemoveAvatar = () => {
    setCurrentAvatarUrl(null);
    setAvatarPreview(null);
    const fileInput = document.getElementById('avatarFile') as HTMLInputElement;
    if (fileInput) {
        fileInput.value = "";
    }
  };


  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentChildIdNumber || !currentName || !currentBirthDate || !currentEnrollmentDate || !currentSpecialistName) { // Added childIdNumber check
        toast({ title: "خطأ", description: "يرجى ملء جميع الحقول الإلزامية.", variant: "destructive" });
        return;
    }

    let finalAvatarUrl = currentAvatarUrl;

    if (currentAvatarUrl === null && editingChild && editingChild.avatarUrl && avatarPreview === null) {
      const initials = currentName.split(' ').map(n => n[0]).join('').substring(0,2).toUpperCase() || '؟؟';
      finalAvatarUrl = `https://placehold.co/100x100.png?text=${encodeURIComponent(initials)}`;
    } else if (currentAvatarUrl === null) {
        const initials = currentName.split(' ').map(n => n[0]).join('').substring(0,2).toUpperCase() || '؟؟';
        finalAvatarUrl = `https://placehold.co/100x100.png?text=${encodeURIComponent(initials)}`;
    }


    if (editingChild) {
      const updatedChildData = {
        ...editingChild,
        childIdNumber: currentChildIdNumber,
        name: currentName,
        birthDate: currentBirthDate,
        enrollmentDate: currentEnrollmentDate, // Save enrollment date
        specialistName: currentSpecialistName,
        avatarUrl: finalAvatarUrl || undefined,
        gender: currentGender,
      };
      const success = updateChild(updatedChildData);
      if (success) {
        toast({ title: "نجاح", description: `تم تحديث بيانات ${currentName} بنجاح.` });
      } else {
        toast({ title: "خطأ", description: "فشل في تحديث بيانات الطفل.", variant: "destructive" });
        return;
      }
    } else {
      const newChild: Child = {
        id: generateUniqueId('child'),
        childIdNumber: currentChildIdNumber,
        name: currentName,
        birthDate: currentBirthDate,
        enrollmentDate: currentEnrollmentDate, // Save enrollment date
        specialistName: currentSpecialistName,
        avatarUrl: finalAvatarUrl || undefined,
        gender: currentGender,
      };
      const success = addChild(newChild);
      if (success) {
        toast({ title: "نجاح", description: `تمت إضافة ${newChild.name} بنجاح.` });
      } else {
        toast({ title: "خطأ", description: "فشل في إضافة الطفل.", variant: "destructive" });
        return;
      }
    }
    setIsFormDialogOpen(false);
    setEditingChild(null);
  };

  const openDeleteConfirmation = (childId: string) => {
    setChildToDeleteId(childId);
  };

  const confirmDeleteChild = () => {
    if (childToDeleteId) {
      const childToDelete = children.find(c => c.id === childToDeleteId);
      const success = removeChild(childToDeleteId);
      if (success) {
        toast({ title: "نجاح", description: `تم حذف ${childToDelete?.name || 'الطفل'} بنجاح.` });
      } else {
        toast({ title: "خطأ", description: "فشل في حذف الطفل.", variant: "destructive" });
      }
      setChildToDeleteId(null);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">جاري تحميل بيانات الأطفال...</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex flex-col sm:flex-row-reverse justify-between items-center mb-6 gap-4">
        <h1 className="text-3xl font-bold text-primary">ملفات تعريف الأطفال</h1>
        <div className="flex gap-2 items-center w-full sm:w-auto">
          <div className="relative w-full sm:w-64">
            <Search className="absolute left-2.5 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="البحث عن أطفال..."
              className="pl-8 w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button onClick={() => handleOpenFormDialog()}>
            <PlusCircle className="mr-2 h-5 w-5" /> إضافة طفل
          </Button>
        </div>
      </div>

      <Dialog open={isFormDialogOpen} onOpenChange={(isOpen) => {
        setIsFormDialogOpen(isOpen);
        if (!isOpen) setEditingChild(null);
      }}>
        <DialogContent className="sm:max-w-[425px] text-right">
          <DialogHeader>
            <DialogTitle className="text-right">{editingChild ? 'تعديل بيانات الطفل' : 'إضافة طفل جديد'}</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleFormSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="childIdNumber" className="text-right col-span-1">رقم الطفل</Label>
                <Input
                  id="childIdNumber"
                  value={currentChildIdNumber}
                  onChange={(e) => setCurrentChildIdNumber(e.target.value)}
                  className="col-span-3 font-mono text-left"
                  placeholder="CH-2024-001"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right col-span-1">الاسم</Label>
                <Input id="name" value={currentName} onChange={(e) => setCurrentName(e.target.value)} className="col-span-3" required />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="birthDate" className="text-right col-span-1">تاريخ الميلاد</Label>
                <Input id="birthDate" type="date" value={currentBirthDate} onChange={(e) => setCurrentBirthDate(e.target.value)} className="col-span-3" required />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="enrollmentDate" className="text-right col-span-1">تاريخ الالتحاق</Label>
                <Input id="enrollmentDate" type="date" value={currentEnrollmentDate} onChange={(e) => setCurrentEnrollmentDate(e.target.value)} className="col-span-3" required />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="specialistName" className="text-right col-span-1">الأخصائي</Label>
                <Input id="specialistName" value={currentSpecialistName} onChange={(e) => setCurrentSpecialistName(e.target.value)} className="col-span-3" required />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="gender" className="text-right col-span-1">الجنس</Label>
                <Select value={currentGender} onValueChange={(value) => setCurrentGender(value as Child['gender'])}>
                  <SelectTrigger id="gender" className="col-span-3">
                    <SelectValue placeholder="اختر الجنس" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">ذكر</SelectItem>
                    <SelectItem value="female">أنثى</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="avatarFile" className="text-right pt-2 col-span-1">صورة شخصية</Label>
                <div className="col-span-3 space-y-3">
                  <div className="flex items-center gap-3 flex-row-reverse">
                    <div className="flex-1">
                      <Input
                        id="avatarFile"
                        type="file"
                        accept="image/*"
                        onChange={handleAvatarChange}
                        className="text-sm"
                      />
                      <p className="text-xs text-muted-foreground mt-1 text-right">
                        اختر صورة بصيغة JPG أو PNG
                      </p>
                    </div>
                    {avatarPreview ? (
                      <div className="relative h-16 w-16 rounded-full overflow-hidden border-2 border-primary shadow-md">
                        <Image src={avatarPreview} alt="معاينة الصورة" fill className="object-cover" />
                      </div>
                    ) : (
                      <div className="h-16 w-16 rounded-full bg-muted border-2 border-dashed border-muted-foreground/30 flex items-center justify-center">
                        <Upload className="h-6 w-6 text-muted-foreground" />
                      </div>
                    )}
                  </div>
                  {avatarPreview && (
                     <Button
                       type="button"
                       variant="outline"
                       size="sm"
                       onClick={handleRemoveAvatar}
                       className="text-destructive hover:text-destructive"
                     >
                        <X className="h-4 w-4 ml-1" />
                        إزالة الصورة
                      </Button>
                  )}
                </div>
              </div>
            </div>
            <DialogFooter className="flex-row-reverse gap-2">
              <Button type="submit">{editingChild ? 'حفظ التعديلات' : 'إضافة الطفل'}</Button>
              <DialogClose asChild>
                <Button type="button" variant="outline">إلغاء</Button>
              </DialogClose>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <AlertDialog open={!!childToDeleteId} onOpenChange={(isOpen) => { if (!isOpen) setChildToDeleteId(null); }}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد من الحذف؟</AlertDialogTitle>
            <AlertDialogDescription>
              سيتم حذف بيانات هذا الطفل بشكل دائم. لا يمكن التراجع عن هذا الإجراء.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setChildToDeleteId(null)}>إلغاء</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteChild} className="bg-destructive hover:bg-destructive/90">
              حذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {filteredChildren.length > 0 ? (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {filteredChildren.map(child => (
            <ChildCard
              key={child.id}
              child={child}
              onEdit={() => handleOpenFormDialog(child)}
              onDelete={() => openDeleteConfirmation(child.id)}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Users className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-2 text-xl font-semibold">لم يتم العثور على أطفال</h3>
          <p className="mt-1 text-sm text-muted-foreground">
            {searchTerm ? "حاول تعديل بحثك." : "ابدأ بإضافة طفل جديد."}
          </p>
        </div>
      )}
    </div>
  );
}
