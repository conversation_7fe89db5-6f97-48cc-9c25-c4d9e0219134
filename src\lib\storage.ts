import type { Child, Assessment, User, LearningPlan, ComprehensiveReport, PlanNote } from './types';
import { MOCK_CHILDREN_DATA, MOCK_ASSESSMENTS_DATA, MOCK_USERS_DATA } from './constants';

// Storage keys
const STORAGE_KEYS = {
  CHILDREN: 'portage_plus_children',
  ASSESSMENTS: 'portage_plus_assessments',
  USERS: 'portage_plus_users',
  LEARNING_PLANS: 'portage_plus_learning_plans',
  COMPREHENSIVE_REPORTS: 'portage_plus_comprehensive_reports',
  PLAN_NOTES: 'portage_plus_plan_notes',
  APP_VERSION: 'portage_plus_version',
  LAST_SYNC: 'portage_plus_last_sync',
} as const;

const APP_VERSION = '1.0.0';

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

// Initialize storage with default data if empty
export function initializeStorage(): void {
  if (!isBrowser) return;

  try {
    // Check if this is the first time or if we need to migrate data
    const currentVersion = localStorage.getItem(STORAGE_KEYS.APP_VERSION);
    const existingChildren = localStorage.getItem(STORAGE_KEYS.CHILDREN);

    if (!currentVersion || !existingChildren) {
      // First time setup - populate with mock data
      localStorage.setItem(STORAGE_KEYS.CHILDREN, JSON.stringify(MOCK_CHILDREN_DATA));
      localStorage.setItem(STORAGE_KEYS.ASSESSMENTS, JSON.stringify(MOCK_ASSESSMENTS_DATA));
      localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(MOCK_USERS_DATA));
      localStorage.setItem(STORAGE_KEYS.LEARNING_PLANS, JSON.stringify([]));
      localStorage.setItem(STORAGE_KEYS.COMPREHENSIVE_REPORTS, JSON.stringify([]));
      localStorage.setItem(STORAGE_KEYS.PLAN_NOTES, JSON.stringify([]));
      localStorage.setItem(STORAGE_KEYS.APP_VERSION, APP_VERSION);
      localStorage.setItem(STORAGE_KEYS.LAST_SYNC, new Date().toISOString());

      console.log('✅ Local storage initialized with default data');
    }
  } catch (error) {
    console.error('❌ Failed to initialize storage:', error);
  }
}

// Generic storage functions
function getFromStorage<T>(key: string, defaultValue: T): T {
  if (!isBrowser) return defaultValue;

  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error(`❌ Failed to get ${key} from storage:`, error);
    return defaultValue;
  }
}

function setToStorage<T>(key: string, value: T): boolean {
  if (!isBrowser) return false;

  try {
    localStorage.setItem(key, JSON.stringify(value));
    localStorage.setItem(STORAGE_KEYS.LAST_SYNC, new Date().toISOString());
    return true;
  } catch (error) {
    console.error(`❌ Failed to set ${key} to storage:`, error);
    return false;
  }
}

// Children storage functions
export function getChildren(): Child[] {
  return getFromStorage(STORAGE_KEYS.CHILDREN, []);
}

export function getChildById(id: string): Child | undefined {
  const children = getChildren();
  return children.find(child => child.id === id);
}

export function saveChild(child: Child): boolean {
  const children = getChildren();
  const existingIndex = children.findIndex(c => c.id === child.id);

  if (existingIndex >= 0) {
    children[existingIndex] = child;
  } else {
    children.unshift(child); // Add new children at the beginning
  }

  return setToStorage(STORAGE_KEYS.CHILDREN, children);
}

export function deleteChild(childId: string): boolean {
  const children = getChildren();
  const filteredChildren = children.filter(c => c.id !== childId);

  // Also delete related assessments and learning plans
  const assessments = getAssessments();
  const filteredAssessments = assessments.filter(a => a.childId !== childId);
  setToStorage(STORAGE_KEYS.ASSESSMENTS, filteredAssessments);

  const learningPlans = getLearningPlans();
  const filteredPlans = learningPlans.filter(p => p.childId !== childId);
  setToStorage(STORAGE_KEYS.LEARNING_PLANS, filteredPlans);

  const comprehensiveReports = getComprehensiveReports();
  const filteredReports = comprehensiveReports.filter(r => r.childId !== childId);
  setToStorage(STORAGE_KEYS.COMPREHENSIVE_REPORTS, filteredReports);

  return setToStorage(STORAGE_KEYS.CHILDREN, filteredChildren);
}

// Assessment storage functions
export function getAssessments(): Assessment[] {
  return getFromStorage(STORAGE_KEYS.ASSESSMENTS, []);
}

export function getAssessmentsByChildId(childId: string): Assessment[] {
  const assessments = getAssessments();
  return assessments.filter(assessment => assessment.childId === childId);
}

export function getAssessmentById(id: string): Assessment | undefined {
  const assessments = getAssessments();
  return assessments.find(assessment => assessment.id === id);
}

export function saveAssessment(assessment: Assessment): boolean {
  const assessments = getAssessments();
  const existingIndex = assessments.findIndex(a => a.id === assessment.id);

  if (existingIndex >= 0) {
    assessments[existingIndex] = assessment;
  } else {
    assessments.unshift(assessment); // Add new assessments at the beginning
  }

  return setToStorage(STORAGE_KEYS.ASSESSMENTS, assessments);
}

export function deleteAssessment(assessmentId: string): boolean {
  const assessments = getAssessments();
  const filteredAssessments = assessments.filter(a => a.id !== assessmentId);

  // Also delete related learning plans
  const learningPlans = getLearningPlans();
  const filteredPlans = learningPlans.filter(p => p.assessmentId !== assessmentId);
  setToStorage(STORAGE_KEYS.LEARNING_PLANS, filteredPlans);

  const comprehensiveReports = getComprehensiveReports();
  const filteredReports = comprehensiveReports.filter(r => r.assessmentId !== assessmentId);
  setToStorage(STORAGE_KEYS.COMPREHENSIVE_REPORTS, filteredReports);

  return setToStorage(STORAGE_KEYS.ASSESSMENTS, filteredAssessments);
}

// User storage functions
export function getUsers(): User[] {
  return getFromStorage(STORAGE_KEYS.USERS, []);
}

export function getUserById(id: string): User | undefined {
  const users = getUsers();
  return users.find(user => user.id === id);
}

export function saveUser(user: User): boolean {
  const users = getUsers();
  const existingIndex = users.findIndex(u => u.id === user.id);

  if (existingIndex >= 0) {
    users[existingIndex] = user;
  } else {
    users.push(user);
  }

  return setToStorage(STORAGE_KEYS.USERS, users);
}

// Learning Plans storage functions
export function getLearningPlans(): LearningPlan[] {
  return getFromStorage(STORAGE_KEYS.LEARNING_PLANS, []);
}

export function getLearningPlansByChildId(childId: string): LearningPlan[] {
  const plans = getLearningPlans();
  return plans.filter(plan => plan.childId === childId);
}

export function saveLearningPlan(plan: LearningPlan): boolean {
  const plans = getLearningPlans();
  const existingIndex = plans.findIndex(p => p.id === plan.id);

  if (existingIndex >= 0) {
    plans[existingIndex] = plan;
  } else {
    plans.unshift(plan);
  }

  return setToStorage(STORAGE_KEYS.LEARNING_PLANS, plans);
}

// Comprehensive Reports storage functions
export function getComprehensiveReports(): ComprehensiveReport[] {
  return getFromStorage(STORAGE_KEYS.COMPREHENSIVE_REPORTS, []);
}

export function getComprehensiveReportsByChildId(childId: string): ComprehensiveReport[] {
  const reports = getComprehensiveReports();
  return reports.filter(report => report.childId === childId);
}

export function saveComprehensiveReport(report: ComprehensiveReport): boolean {
  const reports = getComprehensiveReports();
  const existingIndex = reports.findIndex(r => r.id === report.id);

  if (existingIndex >= 0) {
    reports[existingIndex] = report;
  } else {
    reports.unshift(report);
  }

  return setToStorage(STORAGE_KEYS.COMPREHENSIVE_REPORTS, reports);
}

export function getComprehensiveReportById(reportId: string): ComprehensiveReport | null {
  const reports = getComprehensiveReports();
  return reports.find(r => r.id === reportId) || null;
}

export function deleteComprehensiveReport(reportId: string): boolean {
  const reports = getComprehensiveReports();
  const filteredReports = reports.filter(r => r.id !== reportId);
  return setToStorage(STORAGE_KEYS.COMPREHENSIVE_REPORTS, filteredReports);
}

// Data management functions
export function exportAllData() {
  if (!isBrowser) return null;

  return {
    version: APP_VERSION,
    exportedAt: new Date().toISOString(),
    data: {
      children: getChildren(),
      assessments: getAssessments(),
      users: getUsers(),
      learningPlans: getLearningPlans(),
      comprehensiveReports: getComprehensiveReports(),
      planNotes: getPlanNotes(),
    }
  };
}

export function importAllData(data: any): boolean {
  if (!isBrowser) return false;

  try {
    if (data.data) {
      if (data.data.children) setToStorage(STORAGE_KEYS.CHILDREN, data.data.children);
      if (data.data.assessments) setToStorage(STORAGE_KEYS.ASSESSMENTS, data.data.assessments);
      if (data.data.users) setToStorage(STORAGE_KEYS.USERS, data.data.users);
      if (data.data.learningPlans) setToStorage(STORAGE_KEYS.LEARNING_PLANS, data.data.learningPlans);
      if (data.data.comprehensiveReports) setToStorage(STORAGE_KEYS.COMPREHENSIVE_REPORTS, data.data.comprehensiveReports);
      if (data.data.planNotes) setToStorage(STORAGE_KEYS.PLAN_NOTES, data.data.planNotes);

      localStorage.setItem(STORAGE_KEYS.APP_VERSION, data.version || APP_VERSION);
      localStorage.setItem(STORAGE_KEYS.LAST_SYNC, new Date().toISOString());

      return true;
    }
    return false;
  } catch (error) {
    console.error('❌ Failed to import data:', error);
    return false;
  }
}

export function clearAllData(): boolean {
  if (!isBrowser) return false;

  try {
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
    return true;
  } catch (error) {
    console.error('❌ Failed to clear data:', error);
    return false;
  }
}

// Get storage info
export function getStorageInfo() {
  if (!isBrowser) return null;

  return {
    version: localStorage.getItem(STORAGE_KEYS.APP_VERSION),
    lastSync: localStorage.getItem(STORAGE_KEYS.LAST_SYNC),
    childrenCount: getChildren().length,
    assessmentsCount: getAssessments().length,
    usersCount: getUsers().length,
    learningPlansCount: getLearningPlans().length,
    comprehensiveReportsCount: getComprehensiveReports().length,
    planNotesCount: getPlanNotes().length,
  };
}

// Plan Notes storage functions
export function getPlanNotes(): PlanNote[] {
  return getFromStorage(STORAGE_KEYS.PLAN_NOTES, []);
}

export function getPlanNotesByChildId(childId: string): PlanNote[] {
  const planNotes = getPlanNotes();
  return planNotes.filter(note => note.childId === childId);
}

export function getPlanNotesBySkillId(skillId: string): PlanNote[] {
  const planNotes = getPlanNotes();
  return planNotes.filter(note => note.skillId === skillId);
}

export function getPlanNote(childId: string, skillId: string, planType: 'family' | 'preschool'): PlanNote | undefined {
  const planNotes = getPlanNotes();
  return planNotes.find(note =>
    note.childId === childId &&
    note.skillId === skillId &&
    note.planType === planType
  );
}

export function savePlanNote(planNote: PlanNote): boolean {
  const planNotes = getPlanNotes();
  const existingIndex = planNotes.findIndex(note =>
    note.childId === planNote.childId &&
    note.skillId === planNote.skillId &&
    note.planType === planNote.planType
  );

  if (existingIndex >= 0) {
    // Update existing note
    planNotes[existingIndex] = {
      ...planNote,
      lastModified: new Date().toISOString().split('T')[0]
    };
  } else {
    // Add new note
    planNotes.push(planNote);
  }

  return setToStorage(STORAGE_KEYS.PLAN_NOTES, planNotes);
}

export function deletePlanNote(childId: string, skillId: string, planType: 'family' | 'preschool'): boolean {
  const planNotes = getPlanNotes();
  const filteredNotes = planNotes.filter(note =>
    !(note.childId === childId && note.skillId === skillId && note.planType === planType)
  );
  return setToStorage(STORAGE_KEYS.PLAN_NOTES, filteredNotes);
}
