export interface Child {
  id: string;
  childIdNumber: string; // Unique child identification number (e.g., "CH-2024-001")
  name: string;
  birthDate: string; // ISO string format (YYYY-MM-DD)
  enrollmentDate: string; // ISO string format (YYYY-MM-DD) - Added
  specialistName: string;
  assessmentIds?: string[]; // Store IDs of assessments
  avatarUrl?: string; // Optional URL for child's image
  gender?: 'male' | 'female' | 'other' | 'unknown';
  sessionNotes?: SessionNote[]; // Added for session documentation
  caseStudyNotes?: string; // Added for case study
  caseStudy?: CaseStudyData; // Added for case study data
}

export type SkillStatus = 'yes' | 'no' | 'unclear';
export type ProgressStatus = 'pending' | 'implemented' | 'mastered' | 'postponed';

export interface AssessedSkill {
  skillId: string; // Corresponds to PortageSkillItem.id
  status: SkillStatus;
  notes?: string;
  mediaUrls?: string[]; // URLs to images/videos

  // Fields for progress tracking
  progressStatus?: ProgressStatus;
  implementationStartDate?: string; // ISO string format (YYYY-MM-DD)
  targetCompletionDate?: string; // ISO string format (YYYY-MM-DD)
  progressNotes?: string;
}

export interface Assessment {
  id: string;
  childId: string;
  assessmentDate: string; // ISO string format (YYYY-MM-DD)
  assessedSkills: AssessedSkill[];
  // Calculated fields (optional, can be derived on the fly or stored)
  baselines?: Record<string, string>; // { [subCategoryId]: skillId_of_last_3rd_yes }
  ceilings?: Record<string, string>;  // { [subCategoryId]: skillId_of_1st_of_3_no }
  // Teaching range is dynamically determined based on baselines and ceilings
}

export interface LearningPlan {
  id: string;
  childId: string;
  assessmentId: string;
  generatedDate: string; // ISO string format
  planDetails: string; // Generated by AI (main content)
  suggestedDailyGoals?: string; // Generated by AI
}

export interface ComprehensiveReport {
  id: string;
  childId: string;
  assessmentId: string;
  generatedDate: string; // ISO string format
  childName: string;
  assessmentDate: string; // Date of the assessment this report is based on
  childAgeInMonths: number;
  additionalFocus?: string; // User's additional focus/notes
  executiveSummary: string;
  strengths: string;
  areasForDevelopment: string;
  dataAnalysisHighlights: string;
  actionableRecommendations: string;
}

// Portage Checklist Structure
export interface PortageSkillItem {
  id: string; // Unique ID for the skill, e.g., "soc-rel-0to9-1"
  itemNumber: string; // Display number like "1.1" or "A1"
  ageRange: string; // e.g., "0-9 months", "9-18 months"
  behavior: string; // Description of the skill/behavior in Arabic
  applicationMethod: string; // "ملاحظة مباشرة", "سؤال الأهل", "تجريب مباشر"
  tools?: string; // Optional tools needed, in Arabic
}

export interface PortageSubCategory {
  id: string; // e.g., "soc-rel" (Social-Relationships)
  name: string; // Name of the sub-category in Arabic, e.g., "العلاقات"
  skills: PortageSkillItem[];
}

export interface PortageDimension {
  id: string; // e.g., "soc" (Social)
  name: string; // Name of the dimension in Arabic, e.g., "البعد الاجتماعي"
  subCategories: PortageSubCategory[];
}

// For age calculation display
export interface CalculatedAge {
  years: number;
  months: number;
  days: number;
}

// User Management Types
export type UserRole =
  | 'super_admin'       // إداري الشركة (المتحكم بالموقع)
  | 'eiu_manager'       // مدير وحدة التدخل المبكر
  | 'case_manager'      // مدير الحالة (سابقاً المدير الحالي)
  | 'specialist'        // الأخصائي
  | 'educator'          // المعلم
  | 'viewer';           // مشاهد

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  avatarUrl?: string;
  specialization?: string; // Added specialization field
}

// Session Documentation Type
export interface SessionNote {
  id: string;
  date: string; // ISO string format YYYY-MM-DD
  goalDiscussed: string;
  attendees: string; // Comma-separated or free text
  notes: string;
  nextSteps?: string;
}

// Plan Notes Type
export interface PlanNote {
  id: string;
  childId: string;
  skillId?: string; // Optional - if null/undefined, this is a global note for all skills
  planType: 'family' | 'preschool'; // Type of plan (family or preschool/kindergarten)
  skillBehavior?: string; // The skill behavior for reference (only for skill-specific notes)
  notes: string;
  isGlobal: boolean; // True if this note applies to all skills of this plan type
  createdDate: string; // ISO string format YYYY-MM-DD
  lastModified: string; // ISO string format YYYY-MM-DD
}

// Case Study Types
export interface CaseStudyBasicInfo {
  childName: string;
  birthDate: string;
  currentAge: string;
  gender: 'male' | 'female' | 'other' | 'unknown';
  guardianName: string;
  guardianPhoneNumber: string;
  homeAddress: string;
  guardianRelationship: string;
  hasSiblings: 'yes' | 'no';
  siblingsInfo: string;
}

export interface CaseStudyPregnancyAndBirthInfo {
  motherAgeAtPregnancy: string;
  fullTermPregnancy: 'yes' | 'no';
  prematureBirthMonth: string;
  motherHealthIssuesDuringPregnancy: 'yes' | 'no';
  motherHealthIssuesDetails: string;
  deliveryType: 'natural' | 'cesarean';
  childHealthIssuesAtBirth: 'yes' | 'no';
  childHealthIssuesDetails: string;
}

export interface CaseStudyReinforcerResponseInfo {
  favoriteToys: string;
  enjoyableActivities: string;
  favoriteFoods: string;
  happinessExpression: string;
  motivationMethods: string;
  smilesAtGuardian: 'yes' | 'no' | 'unknown';
  communicatesNeeds: 'yes' | 'no' | 'unknown';
}

export interface CaseStudyData {
  basicInfo: CaseStudyBasicInfo;
  pregnancyAndBirthInfo: CaseStudyPregnancyAndBirthInfo;
  reinforcerResponseInfo: CaseStudyReinforcerResponseInfo;
}

