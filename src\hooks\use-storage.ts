import { useState, useEffect, useCallback } from 'react';
import type { Child, Assessment, User, LearningPlan, ComprehensiveReport, PlanNote } from '@/lib/types';
import {
  initializeStorage,
  getChildren,
  getChildById,
  saveChild,
  deleteChild,
  getAssessments,
  getAssessmentsByChildId,
  getAssessmentById,
  saveAssessment,
  deleteAssessment,
  getUsers,
  getUserById,
  saveUser,
  getLearningPlans,
  getLearningPlansByChildId,
  saveLearningPlan,
  getComprehensiveReports,
  getComprehensiveReportsByChildId,
  saveComprehensiveReport,
  getComprehensiveReportById,
  deleteComprehensiveReport,
  exportAllData,
  importAllData,
  clearAllData,
  getStorageInfo,
  getPlanNotes,
  getPlanNotesByChildId,
  getPlanNotesBySkillId,
  getPlanNote,
  getGlobalPlanNote,
  savePlanNote,
  deletePlanNote,
  deleteGlobalPlanNote,
} from '@/lib/storage';

// Custom hook for children management
export function useChildren() {
  const [children, setChildren] = useState<Child[]>([]);
  const [loading, setLoading] = useState(true);

  const refreshChildren = useCallback(() => {
    setChildren(getChildren());
  }, []);

  useEffect(() => {
    initializeStorage();
    refreshChildren();
    setLoading(false);
  }, [refreshChildren]);

  const addChild = useCallback((child: Child) => {
    const success = saveChild(child);
    if (success) {
      refreshChildren();
    }
    return success;
  }, [refreshChildren]);

  const updateChild = useCallback((child: Child) => {
    const success = saveChild(child);
    if (success) {
      refreshChildren();
    }
    return success;
  }, [refreshChildren]);

  const removeChild = useCallback((childId: string) => {
    const success = deleteChild(childId);
    if (success) {
      refreshChildren();
    }
    return success;
  }, [refreshChildren]);

  const getChild = useCallback((childId: string) => {
    return getChildById(childId);
  }, []);

  return {
    children,
    loading,
    addChild,
    updateChild,
    removeChild,
    getChild,
    refreshChildren,
  };
}

// Custom hook for assessments management
export function useAssessments(childId?: string) {
  const [assessments, setAssessments] = useState<Assessment[]>([]);
  const [loading, setLoading] = useState(true);

  const refreshAssessments = useCallback(() => {
    if (childId) {
      setAssessments(getAssessmentsByChildId(childId));
    } else {
      setAssessments(getAssessments());
    }
  }, [childId]);

  useEffect(() => {
    initializeStorage();
    refreshAssessments();
    setLoading(false);
  }, [refreshAssessments]);

  const addAssessment = useCallback((assessment: Assessment) => {
    const success = saveAssessment(assessment);
    if (success) {
      refreshAssessments();
    }
    return success;
  }, [refreshAssessments]);

  const updateAssessment = useCallback((assessment: Assessment) => {
    const success = saveAssessment(assessment);
    if (success) {
      refreshAssessments();
    }
    return success;
  }, [refreshAssessments]);

  const removeAssessment = useCallback((assessmentId: string) => {
    const success = deleteAssessment(assessmentId);
    if (success) {
      refreshAssessments();
    }
    return success;
  }, [refreshAssessments]);

  const getAssessment = useCallback((assessmentId: string) => {
    return getAssessmentById(assessmentId);
  }, []);

  return {
    assessments,
    loading,
    addAssessment,
    updateAssessment,
    removeAssessment,
    getAssessment,
    refreshAssessments,
  };
}

// Custom hook for users management
export function useUsers() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);

  const refreshUsers = useCallback(() => {
    setUsers(getUsers());
  }, []);

  useEffect(() => {
    initializeStorage();
    refreshUsers();
    setLoading(false);
  }, [refreshUsers]);

  const addUser = useCallback((user: User) => {
    const success = saveUser(user);
    if (success) {
      refreshUsers();
    }
    return success;
  }, [refreshUsers]);

  const updateUser = useCallback((user: User) => {
    const success = saveUser(user);
    if (success) {
      refreshUsers();
    }
    return success;
  }, [refreshUsers]);

  const removeUser = useCallback((userId: string) => {
    // For now, we'll implement a simple filter-based delete
    // In a real app, you might want to add a deleteUser function to storage.ts
    const currentUsers = getUsers();
    const filteredUsers = currentUsers.filter(u => u.id !== userId);

    try {
      localStorage.setItem('portage_plus_users', JSON.stringify(filteredUsers));
      refreshUsers();
      return true;
    } catch (error) {
      console.error('Failed to delete user:', error);
      return false;
    }
  }, [refreshUsers]);

  const getUser = useCallback((userId: string) => {
    return getUserById(userId);
  }, []);

  return {
    users,
    loading,
    addUser,
    updateUser,
    removeUser,
    getUser,
    refreshUsers,
  };
}

// Custom hook for learning plans management
export function useLearningPlans(childId?: string) {
  const [learningPlans, setLearningPlans] = useState<LearningPlan[]>([]);
  const [loading, setLoading] = useState(true);

  const refreshLearningPlans = useCallback(() => {
    if (childId) {
      setLearningPlans(getLearningPlansByChildId(childId));
    } else {
      setLearningPlans(getLearningPlans());
    }
  }, [childId]);

  useEffect(() => {
    initializeStorage();
    refreshLearningPlans();
    setLoading(false);
  }, [refreshLearningPlans]);

  const addLearningPlan = useCallback((plan: LearningPlan) => {
    const success = saveLearningPlan(plan);
    if (success) {
      refreshLearningPlans();
    }
    return success;
  }, [refreshLearningPlans]);

  const updateLearningPlan = useCallback((plan: LearningPlan) => {
    const success = saveLearningPlan(plan);
    if (success) {
      refreshLearningPlans();
    }
    return success;
  }, [refreshLearningPlans]);

  return {
    learningPlans,
    loading,
    addLearningPlan,
    updateLearningPlan,
    refreshLearningPlans,
  };
}

// Custom hook for comprehensive reports management
export function useComprehensiveReports(childId?: string) {
  const [comprehensiveReports, setComprehensiveReports] = useState<ComprehensiveReport[]>([]);
  const [loading, setLoading] = useState(true);

  const refreshComprehensiveReports = useCallback(() => {
    if (childId) {
      setComprehensiveReports(getComprehensiveReportsByChildId(childId));
    } else {
      setComprehensiveReports(getComprehensiveReports());
    }
  }, [childId]);

  useEffect(() => {
    initializeStorage();
    refreshComprehensiveReports();
    setLoading(false);
  }, [refreshComprehensiveReports]);

  const addComprehensiveReport = useCallback((report: ComprehensiveReport) => {
    const success = saveComprehensiveReport(report);
    if (success) {
      refreshComprehensiveReports();
    }
    return success;
  }, [refreshComprehensiveReports]);

  const updateComprehensiveReport = useCallback((report: ComprehensiveReport) => {
    const success = saveComprehensiveReport(report);
    if (success) {
      refreshComprehensiveReports();
    }
    return success;
  }, [refreshComprehensiveReports]);

  const removeComprehensiveReport = useCallback((reportId: string) => {
    const success = deleteComprehensiveReport(reportId);
    if (success) {
      refreshComprehensiveReports();
    }
    return success;
  }, [refreshComprehensiveReports]);

  const getComprehensiveReport = useCallback((reportId: string) => {
    return getComprehensiveReportById(reportId);
  }, []);

  return {
    comprehensiveReports,
    loading,
    addComprehensiveReport,
    updateComprehensiveReport,
    removeComprehensiveReport,
    getComprehensiveReport,
    refreshComprehensiveReports,
  };
}

// Custom hook for data management
export function useDataManagement() {
  const [storageInfo, setStorageInfo] = useState<any>(null);

  const refreshStorageInfo = useCallback(() => {
    setStorageInfo(getStorageInfo());
  }, []);

  useEffect(() => {
    refreshStorageInfo();
  }, [refreshStorageInfo]);

  const exportData = useCallback(() => {
    return exportAllData();
  }, []);

  const importData = useCallback((data: any) => {
    const success = importAllData(data);
    if (success) {
      refreshStorageInfo();
    }
    return success;
  }, [refreshStorageInfo]);

  const clearData = useCallback(() => {
    const success = clearAllData();
    if (success) {
      refreshStorageInfo();
    }
    return success;
  }, [refreshStorageInfo]);

  return {
    storageInfo,
    exportData,
    importData,
    clearData,
    refreshStorageInfo,
  };
}

// Custom hook for plan notes management
export function usePlanNotes(childId?: string, skillId?: string) {
  const [planNotes, setPlanNotes] = useState<PlanNote[]>([]);
  const [loading, setLoading] = useState(true);

  const refreshPlanNotes = useCallback(() => {
    if (childId && skillId) {
      setPlanNotes(getPlanNotesBySkillId(skillId).filter(note => note.childId === childId));
    } else if (childId) {
      setPlanNotes(getPlanNotesByChildId(childId));
    } else if (skillId) {
      setPlanNotes(getPlanNotesBySkillId(skillId));
    } else {
      setPlanNotes(getPlanNotes());
    }
  }, [childId, skillId]);

  useEffect(() => {
    initializeStorage();
    refreshPlanNotes();
    setLoading(false);
  }, [refreshPlanNotes]);

  const addPlanNote = useCallback((planNote: PlanNote) => {
    const success = savePlanNote(planNote);
    if (success) {
      refreshPlanNotes();
    }
    return success;
  }, [refreshPlanNotes]);

  const updatePlanNote = useCallback((planNote: PlanNote) => {
    const success = savePlanNote(planNote);
    if (success) {
      refreshPlanNotes();
    }
    return success;
  }, [refreshPlanNotes]);

  const removePlanNote = useCallback((childId: string, skillId: string, planType: 'family' | 'preschool') => {
    const success = deletePlanNote(childId, skillId, planType);
    if (success) {
      refreshPlanNotes();
    }
    return success;
  }, [refreshPlanNotes]);

  const getPlanNoteByType = useCallback((planType: 'family' | 'preschool') => {
    if (!childId || !skillId) return undefined;
    return getPlanNote(childId, skillId, planType);
  }, [childId, skillId]);

  const getGlobalPlanNoteByType = useCallback((planType: 'family' | 'preschool') => {
    if (!childId) return undefined;
    return getGlobalPlanNote(childId, planType);
  }, [childId]);

  return {
    planNotes,
    loading,
    addPlanNote,
    updatePlanNote,
    removePlanNote,
    getPlanNoteByType,
    getGlobalPlanNoteByType,
    refreshPlanNotes,
  };
}
