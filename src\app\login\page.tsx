import { Brain } from "lucide-react"
import { LoginForm } from "@/components/login-form"
import { APP_NAME } from '@/lib/constants'
import { Badge } from "@/components/ui/badge"

export default function LoginPage() {
  return (
    <div className="relative min-h-svh w-full">
      {/* Login Form - Absolutely positioned to LEFT */}
      <div 
        className="absolute top-0 left-0 w-full lg:w-1/2 h-full flex flex-col gap-4 p-6 md:p-10 bg-background z-10"
        style={{ direction: 'ltr' }}
      >
        <div className="flex justify-center gap-2 md:justify-start">
          <a href="#" className="flex items-center gap-3 font-medium arabic-text">
            <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-primary to-primary/80 text-primary-foreground shadow-sm">
              <Brain className="h-5 w-5" />
            </div>
            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                <span className="text-lg font-bold">
                  {APP_NAME}
                </span>
                <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                  v1.0
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground">
                تنمية الطفولة المبكرة
              </p>
            </div>
          </a>
        </div>
        <div className="flex flex-1 items-center justify-center">
          <div className="w-full max-w-xs">
            <LoginForm />
          </div>
        </div>
      </div>
      
      {/* Image - Absolutely positioned to RIGHT */}
      <div className="hidden lg:block absolute top-0 right-0 w-1/2 h-full bg-muted">
        <img
          src="/placeholder.svg"
          alt="صورة تسجيل الدخول"
          className="absolute inset-0 h-full w-full object-cover dark:brightness-[0.2] dark:grayscale"
        />
      </div>
    </div>
  )
}
