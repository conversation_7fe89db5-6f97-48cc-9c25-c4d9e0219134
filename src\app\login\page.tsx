import { Brain } from "lucide-react"
import { LoginForm } from "@/components/login-form"
import { APP_NAME } from '@/lib/constants'
import { Badge } from "@/components/ui/badge"

export default function LoginPage() {
  return (
    <div className="relative min-h-svh w-full">
      {/* Login Form - Absolutely positioned to LEFT */}
      <div
        className="absolute top-0 left-0 w-full lg:w-1/2 h-full flex flex-col gap-4 p-6 md:p-10 bg-background z-10"
        style={{ direction: 'ltr' }}
      >
        {/* Mobile Logo - Only visible on small screens */}
        <div className="lg:hidden flex flex-col items-center gap-4 mb-8">
          <div className="flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-br from-primary to-primary/80 text-primary-foreground shadow-lg">
            <Brain className="h-10 w-10" />
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-1">
              <span className="text-2xl font-bold text-primary arabic-text">
                {APP_NAME}
              </span>
              <Badge variant="secondary" className="text-xs px-2 py-0.5">
                v1.0
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground arabic-text">
              تنمية الطفولة المبكرة
            </p>
          </div>
        </div>

        {/* Desktop Logo - Only visible on large screens in top corner */}
        <div className="hidden lg:flex justify-start">
          <a href="#" className="flex items-center gap-3 font-medium arabic-text">
            <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-primary to-primary/80 text-primary-foreground shadow-sm">
              <Brain className="h-5 w-5" />
            </div>
            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                <span className="text-lg font-bold">
                  {APP_NAME}
                </span>
                <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                  v1.0
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground">
                تنمية الطفولة المبكرة
              </p>
            </div>
          </a>
        </div>

        <div className="flex flex-1 items-center justify-center">
          <div className="w-full max-w-xs">
            <LoginForm />
          </div>
        </div>
      </div>

      {/* Logo Section - Absolutely positioned to RIGHT */}
      <div className="hidden lg:flex absolute top-0 right-0 w-1/2 h-full bg-gradient-to-br from-primary/5 via-primary/10 to-primary/5 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 right-20 w-40 h-40 rounded-full bg-primary/20"></div>
          <div className="absolute bottom-32 left-16 w-24 h-24 rounded-full bg-primary/15"></div>
          <div className="absolute top-1/2 left-1/3 w-16 h-16 rounded-full bg-primary/10"></div>
        </div>
        <div className="flex flex-col items-center justify-center w-full h-full p-12">
          {/* Large Logo */}
          <div className="flex h-32 w-32 items-center justify-center rounded-3xl bg-gradient-to-br from-primary to-primary/80 text-primary-foreground shadow-2xl mb-8 hover:scale-105 transition-transform duration-300">
            <Brain className="h-16 w-16" />
          </div>

          {/* App Name and Description */}
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center gap-3">
              <h1 className="text-4xl font-bold text-primary arabic-text">
                {APP_NAME}
              </h1>
              <Badge variant="secondary" className="text-sm px-3 py-1">
                v1.0
              </Badge>
            </div>

            <p className="text-xl text-muted-foreground arabic-text font-medium">
              تنمية الطفولة المبكرة
            </p>

            <div className="mt-8 space-y-2 text-center">
              <p className="text-lg text-muted-foreground arabic-text">
                نظام شامل لتقييم ومتابعة نمو الأطفال
              </p>
              <p className="text-base text-muted-foreground/80 arabic-text">
                مدعوم بالذكاء الاصطناعي لتحليل التطور وإنشاء الخطط التعليمية
              </p>
            </div>

            {/* Decorative Elements */}
            <div className="flex justify-center space-x-2 mt-12">
              <div className="h-2 w-2 rounded-full bg-primary/30 animate-bounce" style={{ animationDelay: '0ms' }}></div>
              <div className="h-2 w-2 rounded-full bg-primary/50 animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="h-2 w-2 rounded-full bg-primary animate-bounce" style={{ animationDelay: '300ms' }}></div>
              <div className="h-2 w-2 rounded-full bg-primary/50 animate-bounce" style={{ animationDelay: '450ms' }}></div>
              <div className="h-2 w-2 rounded-full bg-primary/30 animate-bounce" style={{ animationDelay: '600ms' }}></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
