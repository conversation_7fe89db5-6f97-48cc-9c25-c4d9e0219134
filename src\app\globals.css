@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Modern light theme with better colors */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.75rem;
    
    /* Beautiful sidebar colors */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 240 5.9% 90%;
    --sidebar-ring: 240 5.9% 10%;

    /* Custom color system - Light theme */
    --text-50: #fafafa;
    --text-100: #f4f4f5;
    --text-200: #e4e4e7;
    --text-300: #d4d4d8;
    --text-400: #a1a1aa;
    --text-500: #71717a;
    --text-600: #52525b;
    --text-700: #3f3f46;
    --text-800: #27272a;
    --text-900: #18181b;
    --text-950: #09090b;

    --background-50: #fafafa;
    --background-100: #f4f4f5;
    --background-200: #e4e4e7;
    --background-300: #d4d4d8;
    --background-400: #a1a1aa;
    --background-500: #71717a;
    --background-600: #52525b;
    --background-700: #3f3f46;
    --background-800: #27272a;
    --background-900: #18181b;
    --background-950: #09090b;

    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;
    --primary-950: #172554;

    --secondary-50: #f8fafc;
    --secondary-100: #f1f5f9;
    --secondary-200: #e2e8f0;
    --secondary-300: #cbd5e1;
    --secondary-400: #94a3b8;
    --secondary-500: #64748b;
    --secondary-600: #475569;
    --secondary-700: #334155;
    --secondary-800: #1e293b;
    --secondary-900: #0f172a;
    --secondary-950: #020617;

    --accent-50: #f0fdf4;
    --accent-100: #dcfce7;
    --accent-200: #bbf7d0;
    --accent-300: #86efac;
    --accent-400: #4ade80;
    --accent-500: #22c55e;
    --accent-600: #16a34a;
    --accent-700: #15803d;
    --accent-800: #166534;
    --accent-900: #14532d;
    --accent-950: #052e16;
  }

  .dark {
    /* Modern dark theme with better colors */
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    
    /* Beautiful dark sidebar */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 0 0% 98%;
    --sidebar-primary-foreground: 240 5.9% 10%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 240 4.9% 83.9%;

    /* Custom color system - Dark theme */
    --text-50: #09090b;
    --text-100: #18181b;
    --text-200: #27272a;
    --text-300: #3f3f46;
    --text-400: #52525b;
    --text-500: #71717a;
    --text-600: #a1a1aa;
    --text-700: #d4d4d8;
    --text-800: #e4e4e7;
    --text-900: #f4f4f5;
    --text-950: #fafafa;

    --background-50: #09090b;
    --background-100: #18181b;
    --background-200: #27272a;
    --background-300: #3f3f46;
    --background-400: #52525b;
    --background-500: #71717a;
    --background-600: #a1a1aa;
    --background-700: #d4d4d8;
    --background-800: #e4e4e7;
    --background-900: #f4f4f5;
    --background-950: #fafafa;

    --primary-50: #172554;
    --primary-100: #1e3a8a;
    --primary-200: #1e40af;
    --primary-300: #1d4ed8;
    --primary-400: #2563eb;
    --primary-500: #3b82f6;
    --primary-600: #60a5fa;
    --primary-700: #93c5fd;
    --primary-800: #bfdbfe;
    --primary-900: #dbeafe;
    --primary-950: #eff6ff;

    --secondary-50: #020617;
    --secondary-100: #0f172a;
    --secondary-200: #1e293b;
    --secondary-300: #334155;
    --secondary-400: #475569;
    --secondary-500: #64748b;
    --secondary-600: #94a3b8;
    --secondary-700: #cbd5e1;
    --secondary-800: #e2e8f0;
    --secondary-900: #f1f5f9;
    --secondary-950: #f8fafc;

    --accent-50: #052e16;
    --accent-100: #14532d;
    --accent-200: #166534;
    --accent-300: #15803d;
    --accent-400: #16a34a;
    --accent-500: #22c55e;
    --accent-600: #4ade80;
    --accent-700: #86efac;
    --accent-800: #bbf7d0;
    --accent-900: #dcfce7;
    --accent-950: #f0fdf4;
  }
}

@layer base {
  html {
    direction: rtl;
    scroll-behavior: smooth;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  * {
    @apply border-border;
    box-sizing: border-box;
  }
  
  *::before,
  *::after {
    box-sizing: border-box;
  }
  
  body {
    @apply bg-background text-foreground;
    font-family: 'Tajawal', 'Noto Sans Arabic', 'IBM Plex Sans Arabic', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1, "kern" 1;
    line-height: 1.6;
    letter-spacing: 0.01em;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
    min-height: 100vh;
  }

  /* Enhanced Arabic typography */
  .arabic-text {
    font-feature-settings: "rlig" 1, "calt" 1, "kern" 1, "liga" 1;
    text-rendering: optimizeLegibility;
    line-height: 1.7;
    letter-spacing: 0.02em;
  }

  /* Fix sidebar positioning and remove gaps */
  [data-sidebar="sidebar"] {
    @apply w-full h-full;
    direction: rtl;
    text-align: right;
  }

  /* Ensure proper RTL layout for sidebar components */
  [data-sidebar="sidebar"] * {
    direction: rtl;
  }

  /* Remove any unwanted margins that could cause gaps */
  .group\/sidebar-wrapper {
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Fix potential gap issues on the right side */
  [data-side="right"] {
    right: 0 !important;
    border-left: 1px solid hsl(var(--sidebar-border));
    border-right: none !important;
  }

  /* Ensure sidebar content is properly aligned */
  [data-sidebar="content"] {
    width: 100%;
    height: 100%;
    overflow: visible;
  }

  /* Mobile responsive fixes */
  @media (max-width: 768px) {
    .sidebar-fixed {
      position: relative !important;
      width: 100% !important;
      margin: 0 !important;
    }
    
    .main-content {
      margin-right: 0 !important;
      width: 100% !important;
    }

    /* Hide sidebar spacer on mobile */
    [data-side="right"] > div:first-child {
      display: block !important;
      width: 0 !important;
    }

    /* Reset sidebar positioning on mobile */
    [data-side="right"] > div:last-child {
      position: relative !important;
      right: auto !important;
      width: 100% !important;
      height: auto !important;
      top: auto !important;
    }

    /* Adjust main content margins on mobile */
    .layout-main {
      margin-right: 0 !important;
      width: 100% !important;
    }

    /* Prevent horizontal scroll in header */
    header {
      overflow-x: hidden;
      min-width: 0;
    }

    header > div {
      min-width: 0;
      max-width: 100vw;
    }

    /* Ensure search and actions don't overflow */
    .header-search {
      min-width: 0;
      flex-shrink: 1;
    }

    .header-actions {
      flex-shrink: 0;
      min-width: 0;
    }
  }

  /* Desktop sidebar positioning */
  @media (min-width: 769px) {
    .sidebar-fixed {
      position: fixed !important;
      right: 0 !important;
      top: 0 !important;
      bottom: 0 !important;
      width: var(--sidebar-width) !important;
      z-index: 50;
    }
    
    .main-content {
      margin-right: var(--sidebar-width) !important;
      margin-left: 0 !important;
    }

    /* Ensure main content has correct layout on desktop */
    .layout-main {
      margin-right: var(--sidebar-width) !important;
      width: calc(100vw - var(--sidebar-width)) !important;
    }
  }

  /* Ensure proper sidebar background coverage */
  [data-sidebar="sidebar"] {
    background: hsl(var(--sidebar-background));
    border-left: 1px solid hsl(var(--sidebar-border));
    border-right: none;
  }

  /* Hide the spacer div that causes the right gap */
  [data-side="right"] > div:first-child {
    display: none !important;
  }

  /* Force right sidebar to be positioned correctly */
  [data-side="right"] > div:last-child {
    position: fixed !important;
    right: 0 !important;
    left: auto !important;
    width: var(--sidebar-width) !important;
    height: 100vh !important;
    top: 0 !important;
    z-index: 50 !important;
  }

  /* Better scrollbar styling for RTL */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.2);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.3);
  }

  /* Enhanced button and interactive element styles */
  button, [role="button"] {
    cursor: pointer;
    transition: all 0.2s ease-in-out;
  }

  button:disabled, [role="button"]:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  /* Focus styles for better accessibility */
  button:focus-visible,
  [role="button"]:focus-visible,
  input:focus-visible,
  textarea:focus-visible,
  select:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* Smooth transitions */
  .transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }
}

@layer components {
  /* Enhanced card styling */
  .card {
    @apply bg-card text-card-foreground shadow-sm border border-border rounded-lg;
    backdrop-filter: blur(8px);
  }

  /* Better button variants */
  .btn-primary {
    @apply bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50;
    transition: all 0.2s ease-in-out;
  }

  /* Improved input styling */
  .input {
    @apply border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
    direction: rtl;
    text-align: right;
  }

  /* Enhanced sidebar menu styling */
  .sidebar-menu-item {
    @apply rounded-md transition-all duration-200 hover:bg-sidebar-accent/80 focus:bg-sidebar-accent focus:outline-none;
  }

  .sidebar-menu-item.active {
    @apply bg-sidebar-accent text-sidebar-accent-foreground font-medium shadow-sm;
  }
}

@layer utilities {
  /* RTL-specific utilities */
  .rtl {
    direction: rtl;
  }

  .ltr {
    direction: ltr;
  }

  /* Text direction utilities */
  .text-right-rtl {
    text-align: right;
    direction: rtl;
  }

  .text-left-ltr {
    text-align: left;
    direction: ltr;
  }

  /* Spacing utilities for RTL */
  .mr-auto-rtl {
    margin-left: auto;
    margin-right: 0;
  }

  .ml-auto-rtl {
    margin-right: auto;
    margin-left: 0;
  }

  /* Hide scrollbar */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Enhanced hover effects */
  .hover-lift {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  }

  .hover-lift:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  /* Glass effect */
  .glass {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}
