"use client";

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  ClipboardList, 
  Search, 
  Plus, 
  Eye, 
  Edit, 
  Filter,
  Calendar,
  User,
  BarChart3,
  FileText
} from 'lucide-react';
import { useChildren, useAssessments } from '@/hooks/use-storage';
import { formatDate, calculateAge } from '@/lib/utils';
import Link from 'next/link';
import type { Assessment, Child } from '@/lib/types';

interface AssessmentWithChild extends Assessment {
  child: Child | null;
}

export default function AssessmentPage() {
  const { children, loading: childrenLoading } = useChildren();
  const { assessments, loading: assessmentsLoading } = useAssessments();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedChild, setSelectedChild] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'date' | 'child' | 'status'>('date');

  const loading = childrenLoading || assessmentsLoading;

  // Get all assessments with their associated children
  const assessmentsWithChildren: AssessmentWithChild[] = useMemo(() => {
    return assessments.map(assessment => ({
      ...assessment,
      child: children.find(child => child.id === assessment.childId) || null
    }));
  }, [assessments, children]);

  // Filter and sort assessments
  const filteredAssessments = useMemo(() => {
    let filtered = assessmentsWithChildren.filter(assessment => {
      const matchesSearch = 
        (assessment.child?.name.toLowerCase().includes(searchTerm.toLowerCase()) ?? false) ||
        assessment.id.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesChild = selectedChild === 'all' || assessment.childId === selectedChild;
      
      return matchesSearch && matchesChild;
    });

    // Sort assessments
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.assessmentDate).getTime() - new Date(a.assessmentDate).getTime();
        case 'child':
          return (a.child?.name || '').localeCompare(b.child?.name || '');
        case 'status':
          return a.assessedSkills.length - b.assessedSkills.length;
        default:
          return 0;
      }
    });

    return filtered;
  }, [assessmentsWithChildren, searchTerm, selectedChild, sortBy]);

  const getAssessmentStats = (assessment: Assessment) => {
    const total = assessment.assessedSkills.length;
    const achieved = assessment.assessedSkills.filter(skill => skill.status === 'yes').length;
    const needsDevelopment = assessment.assessedSkills.filter(skill => skill.status === 'no').length;
    const unclear = assessment.assessedSkills.filter(skill => skill.status === 'unclear').length;
    
    return { total, achieved, needsDevelopment, unclear };
  };

  const getStatusBadge = (assessment: Assessment) => {
    const { total, achieved } = getAssessmentStats(assessment);
    const percentage = total > 0 ? (achieved / total) * 100 : 0;
    
    if (percentage >= 80) {
      return <Badge variant="default" className="bg-green-500">ممتاز</Badge>;
    } else if (percentage >= 60) {
      return <Badge variant="secondary" className="bg-blue-500 text-white">جيد</Badge>;
    } else if (percentage >= 40) {
      return <Badge variant="outline" className="border-yellow-500 text-yellow-600">متوسط</Badge>;
    } else {
      return <Badge variant="destructive">يحتاج تطوير</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل التقييمات...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold text-primary flex items-center gap-3">
            <ClipboardList className="h-8 w-8" />
            إدارة التقييمات
          </h1>
          <p className="text-muted-foreground">
            عرض وإدارة جميع تقييمات الأطفال في النظام
          </p>
        </div>
        <Link href="/assessment/new">
          <Button className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            تقييم جديد
          </Button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي التقييمات</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{assessmentsWithChildren.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الأطفال المقيمون</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Set(assessmentsWithChildren.map(a => a.childId)).size}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">هذا الشهر</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {assessmentsWithChildren.filter(a => {
                const assessmentDate = new Date(a.assessmentDate);
                const now = new Date();
                return assessmentDate.getMonth() === now.getMonth() && 
                       assessmentDate.getFullYear() === now.getFullYear();
              }).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">متوسط الأداء</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {assessmentsWithChildren.length > 0 ? Math.round(
                assessmentsWithChildren.reduce((sum, assessment) => {
                  const { total, achieved } = getAssessmentStats(assessment);
                  return sum + (total > 0 ? (achieved / total) * 100 : 0);
                }, 0) / assessmentsWithChildren.length
              ) : 0}%
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            البحث والتصفية
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-2.5 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="البحث بالاسم أو رقم التقييم..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-8"
                />
              </div>
            </div>
            <Select value={selectedChild} onValueChange={setSelectedChild}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="اختر طفل" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الأطفال</SelectItem>
                {children.map(child => (
                  <SelectItem key={child.id} value={child.id}>
                    {child.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={sortBy} onValueChange={(value: 'date' | 'child' | 'status') => setSortBy(value)}>
              <SelectTrigger className="w-36">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">التاريخ</SelectItem>
                <SelectItem value="child">الطفل</SelectItem>
                <SelectItem value="status">الحالة</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Assessments Table */}
      <Card>
        <CardHeader>
          <CardTitle>قائمة التقييمات</CardTitle>
          <CardDescription>
            جميع التقييمات المسجلة في النظام مع إمكانية العرض والتعديل
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredAssessments.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-right">الطفل</TableHead>
                  <TableHead className="text-right">تاريخ التقييم</TableHead>
                  <TableHead className="text-right">المهارات</TableHead>
                  <TableHead className="text-right">الحالة</TableHead>
                  <TableHead className="text-right">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAssessments.map((assessment) => {
                  const stats = getAssessmentStats(assessment);
                  return (
                    <TableRow key={assessment.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={assessment.child?.avatarUrl} />
                            <AvatarFallback>
                              {assessment.child?.name?.split(' ').map(n => n[0]).join('') || '؟'}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{assessment.child?.name || 'غير محدد'}</div>
                            <div className="text-sm text-muted-foreground">
                              {assessment.child && calculateAge(assessment.child.birthDate).years} سنة
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          {formatDate(assessment.assessmentDate)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="text-sm">
                            <span className="text-green-600 font-medium">{stats.achieved}</span> متقن
                            {' • '}
                            <span className="text-orange-600 font-medium">{stats.needsDevelopment}</span> يحتاج تطوير
                            {stats.unclear > 0 && (
                              <>
                                {' • '}
                                <span className="text-gray-600 font-medium">{stats.unclear}</span> غير واضح
                              </>
                            )}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            إجمالي: {stats.total} مهارة
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(assessment)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Link href={`/children/${assessment.childId}/assessment/${assessment.id}`}>
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Link href={`/children/${assessment.childId}/assessment/${assessment.id}/edit`}>
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </Link>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8">
              <ClipboardList className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-xl font-semibold">لا توجد تقييمات</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                {searchTerm || selectedChild !== 'all' 
                  ? "لا توجد تقييمات تطابق معايير البحث"
                  : "ابدأ بإنشاء تقييم جديد"
                }
              </p>
              {(!searchTerm && selectedChild === 'all') && (
                <Link href="/assessment/new" className="mt-4 inline-block">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    إنشاء تقييم جديد
                  </Button>
                </Link>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 